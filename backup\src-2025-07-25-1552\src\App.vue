<template>
  <div id="app">
  <el-container>
      <!-- 侧边栏导航 -->
      <el-aside width="250px">
        <div class="logo">
          <h2>Cloudflare 多账户批量工具箱</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          class="el-menu-vertical-demo"
          router
        >
          <el-menu-item index="/batch-add-zone">
            <el-icon><Plus /></el-icon>
            <span>批量添加域名</span>
          </el-menu-item>
          
          <el-menu-item index="/batch-dns-resolver">
            <el-icon><Connection /></el-icon>
            <span>批量域名解析</span>
          </el-menu-item>
          
          <el-menu-item index="/batch-delete-zone">
            <el-icon><Delete /></el-icon>
            <span>批量删除域名</span>
          </el-menu-item>
          
          <el-menu-item index="/batch-ssl-settings">
            <el-icon><Lock /></el-icon>
            <span>批量SSL设置</span>
          </el-menu-item>
          
          <el-menu-item index="/west-batch-dns">
            <el-icon><Monitor /></el-icon>
            <span>西部数码批量DNS</span>
          </el-menu-item>
          
          <el-menu-item index="/reg925-domain-manager">
            <el-icon><Platform /></el-icon>
            <span>925域名管理</span>
          </el-menu-item>
          
          <el-menu-item index="/reg925-batch-dns">
            <el-icon><Connection /></el-icon>
            <span>925批量DNS管理</span>
          </el-menu-item>
          
          <el-menu-item index="/batch-domain-mapping">
            <el-icon><Link /></el-icon>
            <span>批量域名映射</span>
          </el-menu-item>
          
          <el-menu-item index="/r2-manager">
            <el-icon><FolderOpened /></el-icon>
            <span>R2对象存储</span>
          </el-menu-item>
          
          <el-menu-item index="/pages-manager">
            <el-icon><Document /></el-icon>
            <span>Pages批量操作</span>
          </el-menu-item>
          
          <el-divider />
          
          <el-menu-item index="/account-manager">
            <el-icon><User /></el-icon>
            <span>账户管理</span>
          </el-menu-item>
          
          <el-menu-item index="/batch-add-account">
            <el-icon><UserFilled /></el-icon>
            <span>批量添加账户</span>
          </el-menu-item>
          
          <el-menu-item index="/reg925-manager">
            <el-icon><Setting /></el-icon>
            <span>925账户管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区域 -->
    <el-main>
        <router-view />
    </el-main>
  </el-container>
  </div>
</template>

<script>
import { 
  Plus, 
  Connection, 
  Delete, 
  Lock, 
  Monitor, 
  FolderOpened, 
  Document, 
  User, 
  UserFilled,
  Link,
  Platform,
  Setting
} from '@element-plus/icons-vue'

export default {
  name: 'App',
  components: {
    Plus,
    Connection,
    Delete,
    Lock,
    Monitor,
    FolderOpened,
    Document,
    User,
    UserFilled,
    Link,
    Platform,
    Setting
  }
}
</script>

<style>
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  height: 100vh;
  background-color: #f5f5f7;
}

.el-container {
  height: 100%;
}

/* 侧边栏样式 */
.sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-right: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow-y: auto;
}

/* Logo区域 */
.logo {
  padding: 24px 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f2;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 32px;
  line-height: 1;
}

.logo h2 {
  color: #1d1d1f;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: -0.01em;
}

/* 导航分组 */
.nav-section {
  padding: 16px 0 8px 0;
}

.section-title {
  color: #86868b;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.06em;
  padding: 0 20px 8px 20px;
  margin-bottom: 4px;
}

/* 菜单样式 */
.nav-menu {
  border: none;
  background: transparent;
}

.nav-item {
  margin: 0 12px 2px 12px;
  border-radius: 8px;
  height: 44px;
  line-height: 44px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: none !important;
}

.nav-item:hover {
  background-color: #f5f5f7;
}

.nav-item.is-active {
  background-color: #007aff;
  color: #ffffff;
}

.nav-item.is-active:hover {
  background-color: #0051d5;
}

.nav-item-content {
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 100%;
  color: inherit;
}

.nav-icon {
  color: inherit;
  margin-right: 12px;
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  color: inherit;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: -0.005em;
}

.nav-item.is-active .nav-text,
.nav-item.is-active .nav-icon {
  color: #ffffff;
}

.nav-item:not(.is-active) .nav-text {
  color: #1d1d1f;
}

.nav-item:not(.is-active) .nav-icon {
  color: #86868b;
}

/* 主内容区域 */
.main-content {
  padding: 0;
  background-color: #f5f5f7;
  overflow-y: auto;
}

/* 移除Element Plus默认样式 */
.el-menu-item {
  padding: 0 !important;
}

.el-menu-item span {
  font-size: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 260px !important;
  }
  
  .logo h2 {
    font-size: 14px;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #c7c7cc;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background-color: #a8a8a8;
}
</style>