<script>
import axios from 'axios'

export default {
  name: 'BatchDnsResolver',
  data() {
    return {
      operationMode: 'single', // 'single' 或 'batch'
      recordType: 'A',
      recordName: '@',
      recordValue: '***************',
      selectedAccount: '',
      accounts: [],
      domainInput: 'www.api',
      batchDomains: [],
      results: [],
      loading: false,
      
      // 新增：CF域名列表相关
      cfDomains: [],
      selectedDomains: [],
      loadingDomains: false,
      autoFetchDomains: true, // 默认开启自动获取
      
      // 新增：代理状态控制
      enableProxy: true,
      
      // 新增：多账户映射模式相关
      mappingInput: '',
      parsedMappings: [],
      
      // 新增：默认记录值相关
      defaultRecordValue: '***************',
      useDefaultValue: false,

      // 新增：默认记录名称相关
      defaultRecordNames: '@,www,api',
      useDefaultNames: false
    }
  },
  watch: {
    // 监听映射输入变化
    mappingInput: {
      handler: 'parseMappingInput',
      immediate: true
    }
  },
  async mounted() {
    await this.loadAccounts()
  },
  methods: {
    async loadAccounts() {
      try {
        const resp = await axios.get('/api/accounts')
        this.accounts = resp.data
      } catch (e) {
        this.$message.error('加载账户失败：' + e.message)
      }
    },
    
    // 解析映射输入
    parseMappingInput() {
      if (!this.mappingInput.trim()) {
        this.parsedMappings = []
        return
      }
      
      const lines = this.mappingInput.trim().split('\n').filter(line => line.trim())
      const mappings = []
      
      for (const line of lines) {
        // 支持多种分隔符：tab、空格
        const parts = line.trim().split(/[\t\s]+/).filter(p => p)
        if (parts.length >= 2) {
          const email = parts[0]
          const domain = parts[1]

          // 处理记录名称：如果启用默认名称且映射中未指定，则使用默认名称
          let recordNames
          if (this.useDefaultNames && (!parts[2] || parts[2].trim() === '')) {
            recordNames = this.defaultRecordNames
          } else {
            recordNames = parts[2] || '@,www' // 默认记录名称
          }
          
          // 处理记录值：如果启用默认值且映射中未指定，则使用默认值
          let recordValue
          if (this.useDefaultValue && (!parts[3] || parts[3].trim() === '')) {
            recordValue = this.defaultRecordValue
          } else {
            recordValue = parts[3] || this.defaultRecordValue
          }
          
          const accountExists = this.accounts.some(acc => acc.name === email)
          
          mappings.push({
            email,
            domain,
            recordNames,
            recordValue,
            proxy: this.enableProxy,
            accountExists
          })
        }
      }
      
      this.parsedMappings = mappings
    },
    
    // 更新映射预览
    updateMappingPreview() {
      // 重新解析映射输入以更新预览
      this.parseMappingInput()
    },

    // 处理默认记录名称变化
    onUseDefaultNamesChange(value) {
      if (value && this.defaultRecordNames) {
        this.recordName = this.defaultRecordNames
      }
    },
    
    // 新增：获取CF账户下的所有域名
    async fetchCfDomains() {
      if (!this.selectedAccount) {
        this.$message.warning('请先选择CF账户')
        return
      }
      
      this.loadingDomains = true
      try {
        const account = this.accounts.find(acc => acc.id === this.selectedAccount)
        if (!account) {
          this.$message.error('未找到账户信息')
          return
        }
        
        // 通过后端API获取域名列表，避免CORS问题
        const resp = await axios.get('/api/zones', {
          params: {
            email: account.name,
            key: account.token
          }
        })
        
        if (resp.data.result) {
          this.cfDomains = resp.data.result.map(zone => ({
            id: zone.id,
            name: zone.name,
            status: zone.status,
            nameServers: zone.name_servers || []
          }))
          
          this.$message.success(`成功获取 ${this.cfDomains.length} 个域名`)
          
          // 如果开启自动模式，自动选择所有域名
          if (this.autoFetchDomains) {
            this.selectedDomains = this.cfDomains.map(d => d.name)
            this.batchDomains = [...this.selectedDomains]
          }
        } else {
          this.$message.error('获取域名列表失败')
        }
      } catch (e) {
        console.error('获取CF域名列表错误:', e)
        this.$message.error('获取域名列表失败：' + e.message)
      } finally {
        this.loadingDomains = false
      }
    },
    
    // 监听账户选择变化，自动获取域名
    async onAccountChange() {
      if (this.selectedAccount && this.autoFetchDomains) {
        await this.fetchCfDomains()
      }
    },
    
    // 域名选择变化处理
    onDomainSelectionChange(selectedDomains) {
      this.selectedDomains = selectedDomains
      this.batchDomains = [...selectedDomains]
    },
    
    async batchAddDnsRecords() {
      if (!this.selectedAccount) {
        this.$message.error('请选择账户')
        return
      }
      
      // 处理多个记录值（逗号分隔）
      const recordValues = this.recordValue.split(',').map(v => v.trim()).filter(Boolean)
      if (recordValues.length === 0) {
        this.$message.error('请输入记录值')
        return
      }
      
      // 处理多个记录名称（逗号分隔）
      const recordNames = this.recordName.split(',').map(n => n.trim()).filter(Boolean)
      if (recordNames.length === 0) {
        this.$message.error('请输入记录名称')
        return
      }
      
      // 使用选中的域名或手动输入的域名
      let domains = []
      if (this.operationMode === 'batch' && this.selectedDomains.length > 0) {
        domains = this.selectedDomains
      } else if (this.batchDomains) {
        // 处理手动输入的域名列表（按换行符分割）
        domains = typeof this.batchDomains === 'string' 
          ? this.batchDomains.split('\n').map(d => d.trim()).filter(Boolean)
          : this.batchDomains
      }
      
      if (!domains || domains.length === 0) {
        this.$message.error('请选择或输入域名')
        return
      }
      
      this.loading = true
      this.results = []
      
      try {
        const account = this.accounts.find(acc => acc.id === this.selectedAccount)
        
        for (const domain of domains) {
          try {
            // 先获取zone ID
            const zoneResp = await axios.get('/api/zones', {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const zone = zoneResp.data.result?.find(z => z.name === domain.trim())
            if (!zone) {
              this.results.push({
                domain: domain,
                status: '失败',
                message: '域名未在此账户中找到'
              })
              continue
            }
            
            // 获取现有DNS记录
            const existingResp = await axios.get(`/api/zones/${zone.id}/dns_records`, {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const existingRecords = existingResp.data.result || []
            
            // 为每个记录名称和记录值的组合添加DNS记录
            for (const recordName of recordNames) {
              for (const recordValue of recordValues) {
                const actualRecordName = recordName === '@' ? domain : `${recordName}.${domain}`
                
                // 检查是否存在相同名称和类型的记录
                const existingRecord = existingRecords.find(record => 
                  record.type === this.recordType && 
                  record.name === actualRecordName
                )
                
                if (existingRecord) {
                  // 检查是否需要更新（IP不同或代理状态不同）
                  const needUpdate = existingRecord.content !== recordValue || 
                                   ((this.recordType === 'A' || this.recordType === 'AAAA') && 
                                    existingRecord.proxied !== this.enableProxy)
                  
                  if (needUpdate) {
                    try {
                      const updateResp = await axios.put(`/api/zones/${zone.id}/dns_records/${existingRecord.id}`, {
                        email: account.name,
                        key: account.token,
                        type: this.recordType,
                        name: actualRecordName,
                        content: recordValue,
                        proxied: this.recordType === 'A' || this.recordType === 'AAAA' ? this.enableProxy : undefined
                      })
                      
                      if (updateResp.data.success) {
                        this.results.push({
                          domain: domain,
                          recordName: recordName,
                          recordValue: recordValue,
                          status: '更新成功',
                          message: `记录已更新：${existingRecord.content} → ${recordValue}`,
                          recordId: existingRecord.id
                        })
                      } else {
                        this.results.push({
                          domain: domain,
                          recordName: recordName,
                          recordValue: recordValue,
                          status: '更新失败', 
                          message: updateResp.data.errors?.[0]?.message || '更新失败'
                        })
                      }
                    } catch (updateError) {
                      this.results.push({
                        domain: domain,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '更新失败',
                        message: updateError.response?.data?.error || updateError.message
                      })
                    }
                  } else {
                    this.results.push({
                      domain: domain,
                      recordName: recordName,
                      recordValue: recordValue,
                      status: '跳过',
                      message: '记录已存在且配置相同'
                    })
                  }
                } else {
                  // 添加新DNS记录
                  try {
                    const addResp = await axios.post(`/api/zones/${zone.id}/dns_records`, {
                      email: account.name,
                      key: account.token,
                      type: this.recordType,
                      name: actualRecordName,
                      content: recordValue,
                      proxied: this.recordType === 'A' || this.recordType === 'AAAA' ? this.enableProxy : undefined
                    })
                    
                    if (addResp.data.success) {
                      this.results.push({
                        domain: domain,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '成功',
                        message: `${this.recordType} 记录添加成功`,
                        recordId: addResp.data.result.id
                      })
                    } else {
                      this.results.push({
                        domain: domain,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '失败', 
                        message: addResp.data.errors?.[0]?.message || '添加失败'
                      })
                    }
                  } catch (addError) {
                    this.results.push({
                      domain: domain,
                      recordName: recordName,
                      recordValue: recordValue,
                      status: '失败',
                      message: addError.response?.data?.error || addError.message
                    })
                  }
                }
              }
            }
            
          } catch (e) {
            this.results.push({
              domain: domain,
              status: '失败',
              message: e.response?.data?.error || e.message
            })
          }
        }
        
        const successCount = this.results.filter(r => r.status === '成功' || r.status === '更新成功').length
        const totalCount = this.results.length
        this.$message.success(`批量操作完成，成功：${successCount}，总计：${totalCount}`)
        
      } catch (e) {
        this.$message.error('批量操作失败：' + e.message)
      } finally {
        this.loading = false
      }
    },
    
    // 批量映射DNS记录
    async batchMappingDnsRecords() {
      if (this.parsedMappings.length === 0) {
        this.$message.warning('请输入映射关系')
        return
      }
      
      // 检查所有账户是否存在
      const missingAccounts = this.parsedMappings.filter(m => !m.accountExists)
      if (missingAccounts.length > 0) {
        this.$message.error(`以下账户不存在：${missingAccounts.map(m => m.email).join(', ')}`)
        return
      }
      
      this.loading = true
      this.results = []
      
      try {
        for (const mapping of this.parsedMappings) {
          const account = this.accounts.find(acc => acc.name === mapping.email)
          
          try {
            // 先获取zone ID
            const zoneResp = await axios.get('/api/zones', {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const zone = zoneResp.data.result?.find(z => z.name === mapping.domain.trim())
            if (!zone) {
              this.results.push({
                domain: mapping.domain,
                email: mapping.email,
                status: '失败',
                message: '域名未在此账户中找到'
              })
              continue
            }
            
            // 获取现有DNS记录
            const existingResp = await axios.get(`/api/zones/${zone.id}/dns_records`, {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const existingRecords = existingResp.data.result || []
            
            // 解析记录名称（支持逗号分隔）
            const recordNames = mapping.recordNames.split(',').map(name => name.trim()).filter(Boolean)
            
            // 解析记录值（支持逗号分隔）
            const recordValues = mapping.recordValue.split(',').map(value => value.trim()).filter(Boolean)
            
            // 为每个记录名称和记录值的组合添加DNS记录
            for (const recordName of recordNames) {
              for (const recordValue of recordValues) {
                const actualRecordName = recordName === '@' ? mapping.domain : `${recordName}.${mapping.domain}`
                
                // 检查是否存在相同名称和类型的记录
                const existingRecord = existingRecords.find(record => 
                  record.type === this.recordType && 
                  record.name === actualRecordName
                )
                
                if (existingRecord) {
                  // 检查是否需要更新（IP不同或代理状态不同）
                  const needUpdate = existingRecord.content !== recordValue || 
                                   ((this.recordType === 'A' || this.recordType === 'AAAA') && 
                                    existingRecord.proxied !== this.enableProxy)
                  
                  if (needUpdate) {
                    try {
                      const updateResp = await axios.put(`/api/zones/${zone.id}/dns_records/${existingRecord.id}`, {
                        email: account.name,
                        key: account.token,
                        type: this.recordType,
                        name: actualRecordName,
                        content: recordValue,
                        proxied: this.recordType === 'A' || this.recordType === 'AAAA' ? this.enableProxy : undefined
                      })
                      
                      if (updateResp.data.success) {
                        this.results.push({
                          domain: mapping.domain,
                          email: mapping.email,
                          recordName: recordName,
                          recordValue: recordValue,
                          status: '更新成功',
                          message: `记录已更新：${existingRecord.content} → ${recordValue}`,
                          recordId: existingRecord.id
                        })
                      } else {
                        this.results.push({
                          domain: mapping.domain,
                          email: mapping.email,
                          recordName: recordName,
                          recordValue: recordValue,
                          status: '更新失败', 
                          message: updateResp.data.errors?.[0]?.message || '更新失败'
                        })
                      }
                    } catch (updateError) {
                      this.results.push({
                        domain: mapping.domain,
                        email: mapping.email,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '更新失败',
                        message: updateError.response?.data?.error || updateError.message
                      })
                    }
                  } else {
                    this.results.push({
                      domain: mapping.domain,
                      email: mapping.email,
                      recordName: recordName,
                      recordValue: recordValue,
                      status: '跳过',
                      message: '记录已存在且配置相同'
                    })
                  }
                } else {
                  // 添加新DNS记录
                  try {
                    const addResp = await axios.post(`/api/zones/${zone.id}/dns_records`, {
                      email: account.name,
                      key: account.token,
                      type: this.recordType,
                      name: actualRecordName,
                      content: recordValue,
                      proxied: this.recordType === 'A' || this.recordType === 'AAAA' ? this.enableProxy : undefined
                    })
                    
                    if (addResp.data.success) {
                      this.results.push({
                        domain: mapping.domain,
                        email: mapping.email,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '成功',
                        message: `${this.recordType} 记录添加成功`,
                        recordId: addResp.data.result.id
                      })
                    } else {
                      this.results.push({
                        domain: mapping.domain,
                        email: mapping.email,
                        recordName: recordName,
                        recordValue: recordValue,
                        status: '失败', 
                        message: addResp.data.errors?.[0]?.message || '添加失败'
                      })
                    }
                  } catch (addError) {
                    this.results.push({
                      domain: mapping.domain,
                      email: mapping.email,
                      recordName: recordName,
                      recordValue: recordValue,
                      status: '失败',
                      message: addError.response?.data?.error || addError.message
                    })
                  }
                }
              }
            }
            
          } catch (e) {
            this.results.push({
              domain: mapping.domain,
              email: mapping.email,
              status: '失败',
              message: e.response?.data?.error || e.message
            })
          }
        }
        
        const successCount = this.results.filter(r => r.status === '成功' || r.status === '更新成功').length
        const totalCount = this.results.length
        this.$message.success(`批量映射操作完成，成功：${successCount}，总计：${totalCount}`)
        
      } catch (e) {
        this.$message.error('批量映射操作失败：' + e.message)
      } finally {
        this.loading = false
      }
    },
    
    // 删除DNS记录
    async deleteDnsRecord(row) {
      if (!row.recordId) {
        this.$message.warning('无法删除：记录ID不存在')
        return
      }
      
      try {
        // 查找对应的账户
        const account = this.accounts.find(acc => acc.name === row.email)
        if (!account) {
          this.$message.error('未找到对应的账户')
          return
        }
        
        // 获取域名列表以找到zone ID
        const zonesResp = await axios.get('/api/zones', {
          params: {
            email: account.name,
            key: account.token
          }
        })
        
        const zone = zonesResp.data.result?.find(z => z.name === row.domain)
        if (!zone) {
          this.$message.error('未找到对应的域名')
          return
        }
        
        // 删除DNS记录
        const deleteResp = await axios.delete(`/api/zones/${zone.id}/dns_records/${row.recordId}`, {
          params: {
            email: account.name,
            key: account.token
          }
        })
        
        if (deleteResp.data.success) {
          this.$message.success('DNS记录删除成功')
          // 从结果列表中移除该记录
          const index = this.results.findIndex(r => r.recordId === row.recordId)
          if (index > -1) {
            this.results.splice(index, 1)
          }
        } else {
          this.$message.error('DNS记录删除失败')
        }
      } catch (error) {
        console.error('删除DNS记录错误:', error)
        this.$message.error('删除DNS记录失败：' + (error.response?.data?.error || error.message))
      }
    },
    
    // 批量删除DNS记录
    async batchDeleteDnsRecords() {
      if (this.parsedMappings.length === 0) {
        this.$message.warning('请输入映射关系')
        return
      }
      
      // 检查所有账户是否存在
      const missingAccounts = this.parsedMappings.filter(m => !m.accountExists)
      if (missingAccounts.length > 0) {
        this.$message.error(`以下账户不存在：${missingAccounts.map(m => m.email).join(', ')}`)
        return
      }
      
      this.loading = true
      this.results = []
      
      try {
        for (const mapping of this.parsedMappings) {
          const account = this.accounts.find(acc => acc.name === mapping.email)
          
          try {
            // 先获取zone ID
            const zoneResp = await axios.get('/api/zones', {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const zone = zoneResp.data.result?.find(z => z.name === mapping.domain.trim())
            if (!zone) {
              this.results.push({
                domain: mapping.domain,
                email: mapping.email,
                status: '失败',
                message: '域名未在此账户中找到'
              })
              continue
            }
            
            // 获取现有DNS记录
            const existingResp = await axios.get(`/api/zones/${zone.id}/dns_records`, {
              params: {
                email: account.name,
                key: account.token
              }
            })
            
            const existingRecords = existingResp.data.result || []
            
            // 解析记录名称（支持逗号分隔）
            const recordNames = mapping.recordNames.split(',').map(name => name.trim()).filter(Boolean)
            
            // 为每个记录名称删除DNS记录
            for (const recordName of recordNames) {
              const actualRecordName = recordName === '@' ? mapping.domain : `${recordName}.${mapping.domain}`
              
              // 查找要删除的记录
              const existingRecord = existingRecords.find(record => 
                record.type === this.recordType && 
                record.name === actualRecordName
              )
              
              if (existingRecord) {
                try {
                  const deleteResp = await axios.delete(`/api/zones/${zone.id}/dns_records/${existingRecord.id}`, {
                    params: {
                      email: account.name,
                      key: account.token
                    }
                  })
                  
                  if (deleteResp.data.success) {
                    this.results.push({
                      domain: mapping.domain,
                      email: mapping.email,
                      recordName: recordName,
                      recordValue: existingRecord.content,
                      status: '删除成功',
                      message: `DNS记录删除成功`,
                      recordId: existingRecord.id
                    })
                  } else {
                    this.results.push({
                      domain: mapping.domain,
                      email: mapping.email,
                      recordName: recordName,
                      recordValue: existingRecord.content,
                      status: '删除失败', 
                      message: deleteResp.data.errors?.[0]?.message || '删除失败'
                    })
                  }
                } catch (deleteError) {
                  this.results.push({
                    domain: mapping.domain,
                    email: mapping.email,
                    recordName: recordName,
                    recordValue: existingRecord.content,
                    status: '删除失败',
                    message: deleteError.response?.data?.error || deleteError.message
                  })
                }
              } else {
                this.results.push({
                  domain: mapping.domain,
                  email: mapping.email,
                  recordName: recordName,
                  status: '跳过',
                  message: '记录不存在，无需删除'
                })
              }
            }
            
          } catch (e) {
            this.results.push({
              domain: mapping.domain,
              email: mapping.email,
              status: '失败',
              message: e.response?.data?.error || e.message
            })
          }
        }
        
        const successCount = this.results.filter(r => r.status === '删除成功').length
        const totalCount = this.results.length
        this.$message.success(`批量删除操作完成，成功：${successCount}，总计：${totalCount}`)
        
      } catch (e) {
        this.$message.error('批量删除操作失败：' + e.message)
      } finally {
        this.loading = false
      }
    },
    
    getStatusType(status) {
      if (status === '成功' || status === '更新成功' || status === '删除成功') {
        return 'success'
      } else if (status === '失败' || status === '更新失败' || status === '删除失败') {
        return 'danger'
      } else {
        return 'info'
      }
    }
  }
}
</script>

<template>
  <div class="batch-dns-resolver">
    <el-card>
      <h2>批量DNS解析管理</h2>
      
      <!-- 操作模式选择 -->
      <el-form>
        <el-form-item label="操作模式">
          <el-radio-group v-model="operationMode">
            <el-radio value="single">单账户批量模式</el-radio>
            <el-radio value="batch">多账户映射模式</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <!-- 单账户批量模式 -->
      <div v-if="operationMode === 'single'">
      
      <!-- DNS记录配置 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="记录类型">
            <el-select v-model="recordType">
              <el-option label="A" value="A" />
              <el-option label="AAAA" value="AAAA" />
              <el-option label="CNAME" value="CNAME" />
              <el-option label="MX" value="MX" />
              <el-option label="TXT" value="TXT" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="记录名称">
            <el-input v-model="recordName" placeholder="@,www,api (逗号分隔)" />
            <div style="margin-top: 4px; font-size: 12px; color: #909399;">
              支持多个记录名称，用逗号分隔。例如：@,www,api
            </div>
            <!-- 默认记录名称设置 -->
            <div style="margin-top: 8px;">
              <el-checkbox v-model="useDefaultNames" @change="onUseDefaultNamesChange">
                使用默认记录名称
              </el-checkbox>
              <el-input
                v-if="useDefaultNames"
                v-model="defaultRecordNames"
                placeholder="默认记录名称，逗号分隔"
                size="small"
                style="margin-top: 4px;"
              />
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="记录值">
            <el-input v-model="recordValue" placeholder="支持多个值，用逗号分隔：***************,55.250.129.142" />
            <div style="margin-top: 4px; font-size: 12px; color: #909399;">
              支持多个记录值，用逗号分隔。系统会自动检查重复记录。
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 代理状态控制 -->
      <el-form-item label="代理设置" v-if="recordType === 'A' || recordType === 'AAAA'">
        <el-switch 
          v-model="enableProxy" 
          active-text="开启代理 (CF小黄云)" 
          inactive-text="DNS Only (灰云)"
          style="margin-right: 20px;"
        />
        <el-text type="info" size="small">
          A/AAAA记录支持CF代理功能，开启后流量经过CF处理
        </el-text>
      </el-form-item>
      
      <!-- 选择CF账户 -->
      <el-form-item label="选择CF账户">
        <el-select v-model="selectedAccount" placeholder="请选择账户" @change="onAccountChange" style="width: 300px;">
          <el-option v-for="acc in accounts" :key="acc.id" :label="`${acc.name} (${acc.remark || '无备注'})`" :value="acc.id" />
        </el-select>
        
        <!-- 域名获取控制 -->
        <el-checkbox v-model="autoFetchDomains" style="margin-left: 20px;">
          自动获取域名列表
        </el-checkbox>
        
        <el-button @click="fetchCfDomains" :loading="loadingDomains" style="margin-left: 10px;">
          {{ loadingDomains ? '获取中...' : '手动获取域名' }}
        </el-button>
      </el-form-item>
      
      <!-- CF域名列表显示与选择 -->
      <div v-if="cfDomains.length > 0" style="margin-bottom: 20px;">
        <el-card class="domain-list-card">
          <template #header>
            <div class="card-header">
              <span>CF账户域名列表 ({{ cfDomains.length }} 个)</span>
              <el-button size="small" @click="selectedDomains = cfDomains.map(d => d.name)">全选</el-button>
              <el-button size="small" @click="selectedDomains = []">清空</el-button>
            </div>
          </template>
          
          <el-checkbox-group v-model="selectedDomains" @change="onDomainSelectionChange">
            <el-row :gutter="20">
              <el-col :span="8" v-for="domain in cfDomains" :key="domain.id">
                <el-checkbox :value="domain.name" class="domain-checkbox">
                  <div class="domain-info">
                    <div class="domain-name">{{ domain.name }}</div>
                    <el-tag :type="domain.status === 'active' ? 'success' : 'warning'" size="small">
                      {{ domain.status }}
                    </el-tag>
                  </div>
                </el-checkbox>
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-card>
      </div>
      
      <!-- 域名列表 -->
      <el-form-item label="域名列表">
        <el-input
          v-model="batchDomains"
          type="textarea"
          :rows="8"
          placeholder="自动获取的域名列表将显示在这里，也可以手动输入，每行一个域名，例如：&#10;moebelundlicht.com&#10;example.com"
        />
        <div style="margin-top: 10px; color: #666; font-size: 12px;">
          <span v-if="selectedDomains.length > 0">
            已选择 {{ selectedDomains.length }} 个域名
          </span>
          <span v-else>
            请选择域名或手动输入域名列表
          </span>
        </div>
      </el-form-item>
      
      <!-- 开始批量操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="batchAddDnsRecords" :loading="loading">
          {{ loading ? '执行中...' : '开始批量DNS操作' }}
        </el-button>
      </el-form-item>
      </div>
      
      <!-- 多账户映射模式 -->
      <div v-else-if="operationMode === 'batch'">
        <h3>多账户DNS记录映射</h3>
        <el-alert
          title="映射格式说明"
          description="请按照格式输入：邮箱@gmail.com [TAB或空格] 域名.com [TAB或空格] 记录名称 [TAB或空格] IP地址&#10;启用默认值时，可省略记录名称和IP地址，格式：邮箱@gmail.com [TAB或空格] 域名.com"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <!-- DNS记录基本配置 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="6">
            <el-form-item label="记录类型">
              <el-select v-model="recordType">
                <el-option label="A" value="A" />
                <el-option label="AAAA" value="AAAA" />
                <el-option label="CNAME" value="CNAME" />
                <el-option label="MX" value="MX" />
                <el-option label="TXT" value="TXT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="代理设置" v-if="recordType === 'A' || recordType === 'AAAA'">
              <el-switch 
                v-model="enableProxy" 
                active-text="开启代理 (CF小黄云)" 
                inactive-text="DNS Only (灰云)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="默认记录值">
              <el-input 
                v-model="defaultRecordValue" 
                :placeholder="recordType === 'A' ? '默认IP地址' : '默认记录值'"
                @input="updateMappingPreview"
              />
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                {{ recordType === 'A' ? 'A记录默认解析的IP地址' : '默认解析的记录值' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="使用默认值">
              <el-switch
                v-model="useDefaultValue"
                active-text="启用"
                inactive-text="禁用"
                @change="updateMappingPreview"
              />
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                映射中未指定时使用默认值
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 默认记录名称设置 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="默认记录名称">
              <el-input
                v-model="defaultRecordNames"
                placeholder="@,www,api (逗号分隔)"
                @input="updateMappingPreview"
              />
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                多个记录名称用逗号分隔，例如：@,www,api
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用默认记录名称">
              <el-switch
                v-model="useDefaultNames"
                active-text="启用"
                inactive-text="禁用"
                @change="updateMappingPreview"
              />
              <div style="font-size: 12px; color: #666; margin-top: 4px;">
                映射中未指定记录名称时使用默认名称
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="映射关系">
          <el-input
            v-model="mappingInput"
            type="textarea"
            :rows="12"
            placeholder="请输入邮箱-域名映射关系，每行一条，格式：&#10;完整格式：邮箱@gmail.com	域名.com	记录名称	IP地址&#10;简化格式（启用默认值时）：邮箱@gmail.com	域名.com&#10;&#10;示例：&#10;<EMAIL>	eclairagemaison.com	@,www	************&#10;<EMAIL>	homeequippro.com	@,www	************&#10;<EMAIL>	climdeco.com&#10;<EMAIL>	airetdeco.com&#10;<EMAIL>	acheterdeco.com"
            style="width: 100%; font-family: 'Courier New', monospace;"
          />
        </el-form-item>
        
        <!-- 映射预览 -->
        <div v-if="parsedMappings.length > 0" style="margin-top: 20px;">
          <h4>映射预览 ({{ parsedMappings.length }} 条)</h4>
          <el-table :data="parsedMappings" style="width: 100%" max-height="300">
            <el-table-column prop="email" label="CF账户邮箱" width="200" />
            <el-table-column prop="domain" label="域名" width="180" />
            <el-table-column prop="recordNames" label="记录名称" width="120" />
            <el-table-column prop="recordValue" label="记录值" width="150" />
            <el-table-column label="代理状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.proxy ? 'success' : 'info'" size="small">
                  {{ scope.row.proxy ? '开启' : '关闭' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="账户状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.accountExists ? 'success' : 'danger'" size="small">
                  {{ scope.row.accountExists ? '存在' : '不存在' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 开始批量操作按钮 -->
        <el-form-item style="margin-top: 20px;">
          <el-button 
            type="primary" 
            @click="batchMappingDnsRecords" 
            :loading="loading"
            :disabled="parsedMappings.length === 0"
          >
            {{ loading ? '执行中...' : `开始批量映射DNS操作 (${parsedMappings.length} 条)` }}
          </el-button>
          
          <el-button 
            type="danger" 
            @click="batchDeleteDnsRecords" 
            :loading="loading"
            :disabled="parsedMappings.length === 0"
            style="margin-left: 10px;"
          >
            {{ loading ? '执行中...' : `批量删除DNS记录 (${parsedMappings.length} 条)` }}
          </el-button>
        </el-form-item>
      </div>
    </el-card>

    <!-- 任务进度 -->
    <el-card v-if="results.length > 0" style="margin-top: 20px;">
      <template #header>
        <span>任务进度</span>
      </template>
      <el-table :data="results" stripe border>
        <el-table-column prop="email" label="CF账户" width="200" v-if="operationMode === 'batch'" />
        <el-table-column prop="domain" label="域名" width="180" />
        <el-table-column prop="recordName" label="记录名" width="100" />
        <el-table-column prop="recordValue" label="记录值" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="详细信息" />
        <el-table-column label="操作" width="120" v-if="operationMode === 'batch'">
          <template #default="{ row }">
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteDnsRecord(row)"
              :disabled="!row.recordId || row.status === '跳过'"
            >
              删除记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.batch-dns-resolver {
  padding: 20px;
}

.domain-list-card {
  margin: 20px 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.domain-checkbox {
  width: 100%;
  margin-bottom: 10px;
}

.domain-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 5px 0;
}

.domain-name {
  font-weight: 500;
  flex: 1;
}

.el-checkbox__label {
  width: 100%;
}
</style> 