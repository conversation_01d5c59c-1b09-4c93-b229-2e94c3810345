<template>
  <div class="west-batch-dns">
    <el-card>
      <h2>西部数码批量DNS管理</h2>
      
      <!-- 功能模式选择 -->
      <el-form>
        <el-form-item label="管理模式">
          <el-radio-group v-model="managementMode">
            <el-radio value="list">查看域名列表</el-radio>
            <el-radio value="ns">修改域名DNS服务器</el-radio>
            <el-radio value="records">DNS解析记录管理</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <el-form :inline="true">
        <el-form-item label="西部数码账号">
          <el-select v-model="selectedAccountId" placeholder="请选择账号" style="width: 200px;">
            <el-option v-for="acc in validWestAccounts" :key="acc.id" :label="`${acc.username} (${acc.remark || '无备注'})`" :value="acc.id" />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 模式1: 查看域名列表 -->
      <div v-if="managementMode === 'list'">
        <el-card class="mode-card">
          <template #header>
            <div class="card-header">
              <span>域名列表查看</span>
              <el-button type="primary" @click="loadDomainList" :loading="loading">
                {{ loading ? '加载中...' : '刷新列表' }}
              </el-button>
            </div>
          </template>
          
          <el-form :inline="true" style="margin-bottom: 15px;">
            <el-form-item label="域名过滤">
              <el-input v-model="domainFilter" placeholder="输入域名关键词" style="width: 200px;" />
            </el-form-item>
            <el-form-item label="每页显示">
              <el-select v-model="pageSize" style="width: 100px;">
                <el-option label="10" :value="10" />
                <el-option label="20" :value="20" />
                <el-option label="50" :value="50" />
              </el-select>
            </el-form-item>
          </el-form>
          
          <el-table :data="domainList" stripe border v-loading="loading">
            <el-table-column prop="domain" label="域名" width="200" />
            <el-table-column prop="regtime" label="注册时间" width="120" />
            <el-table-column prop="exptime" label="到期时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'warning'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ns" label="DNS服务器" width="180">
              <template #default="{ row }">
                <div v-if="row.ns" style="font-size: 12px;">
                  <div v-for="ns in row.ns.split(',')" :key="ns">{{ ns.trim() }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="viewDomainInfo(row.domain)">详情</el-button>
                <el-button size="small" @click="viewWhois(row.domain)">Whois</el-button>
                <el-button size="small" type="primary" @click="viewDnsRecords(row.domain)">解析记录</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <el-pagination
            v-if="total > 0"
            style="margin-top: 20px;"
            background
            layout="total, sizes, prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </el-card>
      </div>

      <!-- 模式2: 修改域名DNS服务器 -->
      <div v-else-if="managementMode === 'ns'">
        <el-card class="mode-card">
          <template #header>
            <span>批量修改域名DNS服务器</span>
          </template>
          
          <el-alert
            title="提示"
            description="此功能用于将域名的DNS服务器修改为Cloudflare的NS服务器，实现域名解析由CF代管"
            type="info"
            show-icon
            :closable="false"
            style="margin-bottom: 20px;"
          />
          
          <el-form label-width="120px">
            <el-form-item label="域名列表">
              <el-input
                v-model="nsDomains"
                type="textarea"
                :rows="8"
                placeholder="请输入域名，每行一个，例如：&#10;example.com&#10;test.com"
              />
            </el-form-item>
            
            <el-form-item label="DNS服务器">
              <el-input
                v-model="nsServers"
                type="textarea"
                :rows="4"
                placeholder="请输入DNS服务器，每行一个。Cloudflare默认NS：&#10;kay.ns.cloudflare.com&#10;phil.ns.cloudflare.com"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updateNsServers" :loading="loading">
                {{ loading ? '执行中...' : '批量修改NS' }}
              </el-button>
              <el-button @click="useCloudflareNs">使用Cloudflare NS模板</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 模式3: DNS解析记录管理 -->
      <div v-else-if="managementMode === 'records'">
        <el-card class="mode-card">
          <template #header>
            <span>DNS解析记录管理</span>
          </template>
          
          <el-form :inline="true" style="margin-bottom: 15px;">
            <el-form-item label="域名">
              <el-input 
                v-model="currentDomain" 
                placeholder="输入域名查看解析记录" 
                style="width: 200px;"
                @blur="loadDnsRecords"
                @keyup.enter="loadDnsRecords"
              />
            </el-form-item>
            <el-form-item>
              <el-button @click="loadDnsRecords" :loading="dnsLoading">
                {{ dnsLoading ? '查询中...' : '查询解析记录' }}
              </el-button>
            </el-form-item>
          </el-form>
          
          <el-table :data="dnsRecords" stripe border v-loading="dnsLoading">
            <el-table-column prop="hostname" label="主机记录" width="120" />
            <el-table-column prop="record_type" label="记录类型" width="80" />
            <el-table-column prop="record_value" label="记录值" />
            <el-table-column prop="record_line" label="解析线路" width="100" />
            <el-table-column prop="record_ttl" label="TTL" width="80" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button size="small" @click="editRecord(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteRecord(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 结果显示 -->
      <el-card v-if="results.length > 0" style="margin-top: 20px;">
        <template #header>
          <span>操作结果</span>
        </template>
        <el-table :data="results" stripe border>
          <el-table-column prop="domain" label="域名" width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === '成功' ? 'success' : 'danger'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="msg" label="详细信息" />
        </el-table>
      </el-card>
    </el-card>

    <!-- 域名详情对话框 -->
    <el-dialog v-model="showDomainDialog" title="域名详细信息" width="60%">
      <el-descriptions :column="2" border v-if="domainDetail">
        <el-descriptions-item label="域名">{{ domainDetail.domain }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ domainDetail.status }}</el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ domainDetail.regtime }}</el-descriptions-item>
        <el-descriptions-item label="到期时间">{{ domainDetail.exptime }}</el-descriptions-item>
        <el-descriptions-item label="DNS服务器" :span="2">
          <div v-if="domainDetail.ns">
            <el-tag v-for="ns in domainDetail.ns.split(',')" :key="ns" style="margin-right: 8px;">
              {{ ns.trim() }}
            </el-tag>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- Whois信息对话框 -->
    <el-dialog v-model="showWhoisDialog" title="Whois信息" width="80%">
      <el-input
        v-if="whoisInfo"
        :modelValue="whoisInfo"
        type="textarea"
        :rows="20"
        readonly
        style="font-family: monospace; font-size: 12px;"
      />
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'WestBatchDNS',
  data() {
    return {
      managementMode: 'list',
      selectedAccountId: '',
      westAccounts: [],
      loading: false,
      dnsLoading: false,
      
      // 域名列表相关
      domainList: [],
      domainFilter: '',
      pageSize: 20,
      currentPage: 1,
      total: 0,
      
      // NS修改相关
      nsDomains: '',
      nsServers: 'kay.ns.cloudflare.com\nphil.ns.cloudflare.com',
      
      // DNS记录管理相关
      currentDomain: '',
      dnsRecords: [],
      
      // 通用
      results: [],
      
      // 对话框相关
      showDomainDialog: false,
      domainDetail: null,
      showWhoisDialog: false,
      whoisInfo: ''
    }
  },
  computed: {
    // 过滤有效的西部数码账户，避免undefined值
    validWestAccounts() {
      return this.westAccounts.filter(acc => 
        acc && 
        acc.id !== undefined && 
        acc.id !== null && 
        (acc.username || acc.id)
      )
    }
  },
  async mounted() {
    await this.loadWestAccounts()
  },
  methods: {
    async loadWestAccounts() {
      try {
        console.log('正在加载西部数码账户...')
        const resp = await axios.get('http://localhost:3000/api/west/accounts')
        console.log('西部数码账户响应:', resp.data)
        this.westAccounts = resp.data
        console.log('设置后的westAccounts:', this.westAccounts)
      } catch (e) {
        console.error('加载西部数码账户错误:', e)
        this.$message.error('加载西部数码账户失败：' + e.message)
      }
    },
    
    async loadDomainList() {
      if (!this.selectedAccountId) {
        this.$message.warning('请先选择西部数码账号')
        return
      }
      
      this.loading = true
      try {
        const resp = await axios.post('/api/west/domains/list', {
          account_id: this.selectedAccountId,
          domain: this.domainFilter,
          limit: this.pageSize,
          page: this.currentPage
        })
        
        if (resp.data.code === 200) {
          this.domainList = resp.data.data || []
          this.total = resp.data.total || 0
          this.$message.success(`成功加载 ${this.domainList.length} 个域名`)
        } else {
          this.$message.error('获取域名列表失败：' + resp.data.msg)
        }
      } catch (e) {
        this.$message.error('获取域名列表失败：' + e.message)
        console.error('Domain list error:', e)
      } finally {
        this.loading = false
      }
    },
    
    async viewDomainInfo(domain) {
      if (!this.selectedAccountId) {
        this.$message.warning('请先选择西部数码账号')
        return
      }
      
      try {
        const resp = await axios.post('/api/west/domains/info', {
          account_id: this.selectedAccountId,
          domain: domain
        })
        
        if (resp.data.code === 200 && resp.data.data && resp.data.data.length > 0) {
          this.domainDetail = resp.data.data[0]
          this.showDomainDialog = true
        } else {
          this.$message.error('获取域名信息失败：' + resp.data.msg)
        }
      } catch (e) {
        this.$message.error('获取域名信息失败：' + e.message)
      }
    },
    
    async viewWhois(domain) {
      if (!this.selectedAccountId) {
        this.$message.warning('请先选择西部数码账号')
        return
      }
      
      try {
        const resp = await axios.post('/api/west/domains/whois', {
          account_id: this.selectedAccountId,
          domain: domain
        })
        
        if (resp.data.code === 200) {
          this.whoisInfo = resp.data.data || '无Whois信息'
          this.showWhoisDialog = true
        } else {
          this.$message.error('获取Whois信息失败：' + resp.data.msg)
        }
      } catch (e) {
        this.$message.error('获取Whois信息失败：' + e.message)
      }
    },
    
    async viewDnsRecords(domain) {
      this.currentDomain = domain
      this.managementMode = 'records'
      this.$nextTick(() => {
        this.loadDnsRecords()
      })
    },
    
    async loadDnsRecords() {
      if (!this.selectedAccountId || !this.currentDomain) {
        this.$message.warning('请先选择账号和输入域名')
        return
      }
      
      this.dnsLoading = true
      try {
        const account = this.westAccounts.find(acc => acc.id === this.selectedAccountId)
        if (!account) {
          this.$message.error('账户信息不存在')
          return
        }
        
        const resp = await axios.post('/api/west/dns/list', {
          username: account.username,
          api_password: account.api_password,
          domain: this.currentDomain
        })
        
        if (resp.data.code === 200) {
          this.dnsRecords = resp.data.data || []
          this.$message.success(`成功加载 ${this.dnsRecords.length} 条解析记录`)
        } else {
          this.$message.error('获取DNS记录失败：' + resp.data.msg)
        }
      } catch (e) {
        this.$message.error('获取DNS记录失败：' + e.message)
      } finally {
        this.dnsLoading = false
      }
    },
    
    async updateNsServers() {
      if (!this.selectedAccountId || !this.nsDomains.trim() || !this.nsServers.trim()) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.loading = true
      this.results = []
      
      try {
        const account = this.westAccounts.find(acc => acc.id === this.selectedAccountId)
        if (!account) {
          this.$message.error('账户信息不存在')
          return
        }
        
        const domains = this.nsDomains.split(/\r?\n/).map(d => d.trim()).filter(Boolean)
        const nsServers = this.nsServers.split(/\r?\n/).map(ns => ns.trim()).filter(Boolean)
        
        for (const domain of domains) {
          try {
            const resp = await axios.post('/api/west/domains/update-ns', {
              username: account.username,
              api_password: account.api_password,
              domains: [domain],
              ns_list: nsServers
            })
            
            const success = resp.data.code === 200
            this.results.push({
              domain,
              status: success ? '成功' : '失败',
              msg: resp.data.msg || (success ? 'NS设置成功' : 'NS设置失败')
            })
          } catch (e) {
            this.results.push({
              domain,
              status: '失败',
              msg: e.response?.data?.msg || e.message
            })
          }
        }
        
        const successCount = this.results.filter(r => r.status === '成功').length
        this.$message.success(`批量NS设置完成，成功：${successCount}，失败：${this.results.length - successCount}`)
      } catch (e) {
        this.$message.error('批量NS设置失败：' + e.message)
      } finally {
        this.loading = false
      }
    },
    
    useCloudflareNs() {
      this.nsServers = 'kay.ns.cloudflare.com\nphil.ns.cloudflare.com'
      this.$message.success('已填入Cloudflare NS服务器')
    },
    
    handlePageChange(page) {
      this.currentPage = page
      this.loadDomainList()
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadDomainList()
    },
    
    editRecord(record) {
      this.$message.info('编辑功能开发中...')
    },
    
    deleteRecord(record) {
      this.$message.info('删除功能开发中...')
    }
  }
}
</script>

<style scoped>
.west-batch-dns {
  padding: 20px;
}

.mode-card {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-table {
  margin-top: 15px;
}

.el-pagination {
  display: flex;
  justify-content: center;
}

.el-descriptions {
  margin-top: 20px;
}

.el-tag {
  margin: 2px;
}
</style>