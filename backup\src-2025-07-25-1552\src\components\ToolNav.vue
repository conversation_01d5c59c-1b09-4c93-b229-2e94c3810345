<template>
  <div class="tool-nav-container">
    <h1 class="main-title">Cloudflare 多账户批量管理工具</h1>
    <p class="subtitle">支持多账户操作，批量管理域名、DNS、SSL证书、R2存储和Pages静态页面</p>
    
    <div class="tools-grid">
      <div class="tool-card" @click="navigateTo('/batch-add-zone')">
        <div class="tool-icon">🌐</div>
        <h3>批量添加域名</h3>
        <p>快速批量添加域名到Cloudflare，支持多账户操作</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/batch-dns')">
        <div class="tool-icon">🔧</div>
        <h3>批量域名解析</h3>
        <p>批量管理DNS解析记录，支持A、CNAME、MX等类型</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/batch-delete-zone')">
        <div class="tool-icon">🗑️</div>
        <h3>批量删除域名</h3>
        <p>批量删除不需要的域名，小心操作</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/batch-ssl')">
        <div class="tool-icon">🔒</div>
        <h3>批量SSL设置</h3>
        <p>批量配置SSL/HTTPS加密模式，保护网站安全</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/west-batch-dns')">
        <div class="tool-icon">🇨🇳</div>
        <h3>西部数码批量DNS</h3>
        <p>批量修改西部数码域名DNS服务器</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/batch-r2')">
        <div class="tool-icon">📦</div>
        <h3>R2对象存储</h3>
        <p>管理Cloudflare R2对象存储服务</p>
      </div>
      
      <div class="tool-card" @click="navigateTo('/batch-pages')">
        <div class="tool-icon">📄</div>
        <h3>Pages批量操作</h3>
        <p>管理Cloudflare Pages静态网站项目</p>
      </div>
      
      <div class="tool-card account-card" @click="navigateTo('/account')">
        <div class="tool-icon">👤</div>
        <h3>账户管理</h3>
        <p>管理Cloudflare和西部数码账户信息</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

function navigateTo(path) {
  router.push(path)
}
</script>

<style scoped>
.tool-nav-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-title {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 2.5em;
  font-weight: bold;
}

.subtitle {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 40px;
  font-size: 1.1em;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.tool-card {
  background: #fff;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e1e8ed;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tool-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: #3498db;
}

.tool-icon {
  font-size: 3em;
  margin-bottom: 15px;
}

.tool-card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.3em;
}

.tool-card p {
  color: #7f8c8d;
  line-height: 1.5;
  font-size: 0.95em;
}

.account-card {
  border-color: #e74c3c;
}

.account-card:hover {
  border-color: #c0392b;
}
</style> 