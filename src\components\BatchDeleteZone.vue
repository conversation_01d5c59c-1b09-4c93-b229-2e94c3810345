<template>
  <div class="batch-delete-zone">
    <el-card>
      <h2>批量删除域名/Zone</h2>

      <!-- 批量操作模式选择 -->
      <el-form>
        <el-form-item label="操作模式">
          <el-radio-group v-model="operationMode" @change="onModeChange">
            <el-radio value="single">单账户批量模式</el-radio>
            <el-radio value="mapping">多账户映射模式</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <!-- 单账户批量模式 -->
      <div v-if="operationMode === 'single'">
        <el-form>
          <el-form-item label="选择CF账户">
            <el-select v-model="selectedAccount" placeholder="请选择账户" @change="onAccountChange" style="width: 300px;">
              <el-option v-for="acc in accounts" :key="acc.id" :label="`${acc.name} (${acc.remark || '无备注'})`" :value="acc.id" />
            </el-select>

            <el-button @click="fetchCfDomains" :loading="loadingDomains" style="margin-left: 10px;">
              {{ loadingDomains ? '获取中...' : '获取域名列表' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 多账户映射模式 -->
      <div v-if="operationMode === 'mapping'">
        <el-form>
          <el-form-item label="域名-账户映射">
            <el-input
              type="textarea"
              v-model="domainMappings"
              :rows="8"
              placeholder="请按照格式输入：邮箱@gmail.com [TAB或空格] 域名.com [TAB或空格]&#10;例如：&#10;<EMAIL>	example.com&#10;<EMAIL>	test.com&#10;<EMAIL>	demo.net&#10;输入后自动映射预览"
              style="width: 600px;"
            />
          </el-form-item>

          <el-form-item>
            <el-button @click="parseMappings" :loading="loadingMappings">
              {{ loadingMappings ? '解析中...' : '解析映射关系' }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 解析后的映射列表 -->
        <div v-if="parsedMappings.length > 0">
          <h3>解析结果 ({{ parsedMappings.length }} 个映射)</h3>
          <el-table
            ref="mappingTable"
            :data="parsedMappings"
            border
            style="width: 100%; margin-bottom: 20px;"
            @selection-change="handleMappingSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="domain" label="域名" width="200" />
            <el-table-column prop="email" label="账户邮箱" width="250" />
            <el-table-column prop="accountName" label="账户备注" width="150" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === '有效' ? 'success' : 'danger'">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="备注" />
          </el-table>

          <!-- 快速选择按钮 -->
          <div style="margin-bottom: 20px;">
            <el-button @click="selectAllMappings" size="small">全选</el-button>
            <el-button @click="selectNoneMappings" size="small">取消全选</el-button>
            <el-button @click="selectValidMappings" size="small">选择有效映射</el-button>
          </div>
        </div>
      </div>

      <!-- 危险操作警告 -->
      <el-alert
        title="⚠️ 危险操作警告"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;"
      >
        <p>删除操作不可逆，请谨慎选择要删除的域名！</p>
        <p>删除后将失去对该域名的所有CF配置，包括DNS解析、SSL证书等。</p>
      </el-alert>

      <!-- 单账户模式：域名列表 -->
      <div v-if="operationMode === 'single' && cfDomains.length > 0">
        <h3>当前账户域名列表 ({{ cfDomains.length }} 个)</h3>
        <el-checkbox-group v-model="selectedDomains" style="margin-bottom: 20px;">
          <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px;">
            <el-checkbox
              v-for="domain in cfDomains"
              :key="domain.id"
              :value="domain.name"
              style="margin: 0;"
            >
              <div style="display: flex; flex-direction: column; align-items: flex-start;">
                <span style="font-weight: bold;">{{ domain.name }}</span>
                <span style="font-size: 12px; color: #666;">
                  状态: {{ domain.status }} | Zone ID: {{ domain.id }}
                </span>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>

        <!-- 快速选择按钮 -->
        <div style="margin-bottom: 20px;">
          <el-button @click="selectAll" size="small">全选</el-button>
          <el-button @click="selectNone" size="small">取消全选</el-button>
          <el-button @click="selectInactive" size="small">选择非活跃域名</el-button>
        </div>
      </div>

      <!-- 确认删除输入 -->
      <div v-if="(operationMode === 'single' && selectedDomains.length > 0) || (operationMode === 'mapping' && selectedMappings.length > 0)" style="margin-bottom: 20px;">
        <el-alert
          title="请输入 'DELETE' 确认删除操作"
          type="error"
          :closable="false"
          style="margin-bottom: 10px;"
        />
        <el-input
          v-model="confirmText"
          placeholder="请输入 DELETE 确认删除"
          style="width: 300px;"
        />
      </div>

      <!-- 开始批量删除按钮 -->
      <el-form-item>
        <el-button
          type="danger"
          @click="batchDeleteZones"
          :loading="loading"
          :disabled="!canDelete"
        >
          {{ loading ? '删除中...' : getDeleteButtonText }}
        </el-button>
      </el-form-item>
    </el-card>

    <!-- 删除结果 -->
    <el-card v-if="results.length > 0" style="margin-top: 20px;">
      <h3>删除结果</h3>
      <el-table :data="results" border style="width: 100%">
        <el-table-column prop="domain" label="域名" width="200" />
        <el-table-column prop="zoneId" label="Zone ID" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="详细信息" />
      </el-table>

      <!-- 导出结果 -->
      <div style="margin-top: 20px;">
        <el-button @click="exportResults">导出删除结果</el-button>
        <el-button @click="clearResults">清空结果</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'BatchDeleteZone',
  data() {
    return {
      operationMode: 'single', // 'single' 或 'mapping'
      selectedAccount: '',
      accounts: [],
      cfDomains: [],
      selectedDomains: [],
      loadingDomains: false,
      loading: false,
      confirmText: '',
      results: [],
      // 多账户映射模式相关
      domainMappings: '',
      parsedMappings: [],
      selectedMappings: [],
      loadingMappings: false
    }
  },
  computed: {
    canDelete() {
      if (this.operationMode === 'single') {
        return this.selectedDomains.length > 0 && this.confirmText === 'DELETE'
      } else {
        return this.selectedMappings.length > 0 && this.confirmText === 'DELETE'
      }
    },
    getDeleteButtonText() {
      if (this.operationMode === 'single') {
        return `确认删除 ${this.selectedDomains.length} 个域名`
      } else {
        return `确认删除 ${this.selectedMappings.length} 个域名`
      }
    }
  },
  async mounted() {
    await this.loadAccounts()
  },
  methods: {
    async loadAccounts() {
      try {
        const resp = await axios.get('/api/accounts')
        this.accounts = resp.data
      } catch (e) {
        this.$message.error('加载账户失败：' + e.message)
      }
    },

    async fetchCfDomains() {
      if (!this.selectedAccount) {
        this.$message.warning('请先选择CF账户')
        return
      }

      this.loadingDomains = true
      try {
        const account = this.accounts.find(acc => acc.id === this.selectedAccount)
        if (!account) {
          this.$message.error('未找到账户信息')
          return
        }

        const resp = await axios.get('/api/zones', {
          params: {
            email: account.name,
            key: account.token
          }
        })

        if (resp.data.result) {
          this.cfDomains = resp.data.result.map(zone => ({
            id: zone.id,
            name: zone.name,
            status: zone.status,
            nameServers: zone.name_servers || []
          }))

          this.$message.success(`成功获取 ${this.cfDomains.length} 个域名`)
        } else {
          this.$message.error('获取域名列表失败')
        }
      } catch (e) {
        console.error('获取CF域名列表错误:', e)
        this.$message.error('获取域名列表失败：' + e.message)
      } finally {
        this.loadingDomains = false
      }
    },

    async onAccountChange() {
      this.selectedDomains = []
      this.confirmText = ''
      this.results = []
      if (this.selectedAccount) {
        await this.fetchCfDomains()
      }
    },

    onModeChange() {
      // 切换模式时重置状态
      this.selectedDomains = []
      this.selectedMappings = []
      this.confirmText = ''
      this.results = []
      this.domainMappings = ''
      this.parsedMappings = []
    },

    selectAll() {
      this.selectedDomains = this.cfDomains.map(d => d.name)
    },

    selectNone() {
      this.selectedDomains = []
    },

    selectInactive() {
      this.selectedDomains = this.cfDomains
        .filter(d => d.status !== 'active')
        .map(d => d.name)
    },

    // 映射模式相关方法
    async parseMappings() {
      if (!this.domainMappings.trim()) {
        this.$message.warning('请输入域名-账户映射')
        return
      }

      this.loadingMappings = true
      this.parsedMappings = []

      try {
        const lines = this.domainMappings.split(/\r?\n/).filter(line => line.trim())

        for (const line of lines) {
          // 支持 TAB 或空格分隔：邮箱 [TAB/空格] 域名
          const parts = line.split(/[\t\s]+/).filter(part => part.trim())

          if (parts.length < 2) {
            this.parsedMappings.push({
              domain: parts[1] || '未知',
              email: parts[0] || '未知',
              accountName: '',
              status: '格式错误',
              message: '格式应为：邮箱@gmail.com [TAB或空格] 域名.com'
            })
            continue
          }

          const email = parts[0].trim()
          const domain = parts[1].trim()

          if (!domain || !email) {
            this.parsedMappings.push({
              domain: domain || '未知',
              email: email || '未知',
              accountName: '',
              status: '格式错误',
              message: '格式应为：邮箱@gmail.com [TAB或空格] 域名.com'
            })
            continue
          }

          // 查找对应的账户
          const account = this.accounts.find(acc => acc.name === email)
          if (!account) {
            this.parsedMappings.push({
              domain,
              email,
              accountName: '',
              status: '账户不存在',
              message: '未找到对应的CF账户'
            })
            continue
          }

          this.parsedMappings.push({
            domain,
            email,
            accountName: account.remark || '无备注',
            status: '有效',
            message: '映射有效'
          })
        }

        this.$message.success(`解析完成，共 ${this.parsedMappings.length} 个映射`)
      } catch (e) {
        this.$message.error('解析映射失败：' + e.message)
      } finally {
        this.loadingMappings = false
      }
    },

    selectAllMappings() {
      this.$nextTick(() => {
        this.$refs.mappingTable.toggleAllSelection()
      })
    },

    selectNoneMappings() {
      this.$nextTick(() => {
        this.$refs.mappingTable.clearSelection()
      })
    },

    selectValidMappings() {
      this.$nextTick(() => {
        this.$refs.mappingTable.clearSelection()
        this.parsedMappings.forEach((row) => {
          if (row.status === '有效') {
            this.$refs.mappingTable.toggleRowSelection(row, true)
          }
        })
      })
    },

    handleMappingSelectionChange(selection) {
      this.selectedMappings = selection
    },

    async batchDeleteZones() {
      if (this.confirmText !== 'DELETE') {
        this.$message.error('请输入 DELETE 确认删除操作')
        return
      }

      if (this.operationMode === 'single') {
        return await this.batchDeleteSingleMode()
      } else {
        return await this.batchDeleteMappingMode()
      }
    },

    async batchDeleteSingleMode() {
      if (!this.selectedAccount) {
        this.$message.error('请选择账户')
        return
      }

      if (this.selectedDomains.length === 0) {
        this.$message.error('请选择要删除的域名')
        return
      }

      // 最后确认
      const confirmed = await this.$confirm(
        `确定要删除 ${this.selectedDomains.length} 个域名吗？此操作不可逆！`,
        '最终确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error',
          confirmButtonClass: 'el-button--danger'
        }
      ).catch(() => false)

      if (!confirmed) {
        return
      }

      this.loading = true
      this.results = []

      try {
        const account = this.accounts.find(acc => acc.id === this.selectedAccount)

        for (const domainName of this.selectedDomains) {
          try {
            const domain = this.cfDomains.find(d => d.name === domainName)
            if (!domain) {
              this.results.push({
                domain: domainName,
                zoneId: '',
                status: '失败',
                message: '未找到对应的Zone信息'
              })
              continue
            }

            // 删除Zone
            const resp = await axios.delete(`/api/zones/${domain.id}`, {
              data: {
                email: account.name,
                key: account.token
              }
            })

            if (resp.data.success) {
              this.results.push({
                domain: domainName,
                zoneId: domain.id,
                status: '成功',
                message: 'Zone删除成功'
              })
            } else {
              this.results.push({
                domain: domainName,
                zoneId: domain.id,
                status: '失败',
                message: resp.data.errors?.[0]?.message || '删除失败'
              })
            }
          } catch (e) {
            this.results.push({
              domain: domainName,
              zoneId: '',
              status: '失败',
              message: e.response?.data?.error || e.message
            })
          }
        }

        const successCount = this.results.filter(r => r.status === '成功').length
        const failCount = this.results.filter(r => r.status === '失败').length

        this.$message.success(`删除完成！成功: ${successCount} 个，失败: ${failCount} 个`)

        // 刷新域名列表
        if (successCount > 0) {
          await this.fetchCfDomains()
          this.selectedDomains = []
          this.confirmText = ''
        }

      } catch (e) {
        console.error('批量删除错误:', e)
        this.$message.error('批量删除失败：' + e.message)
      } finally {
        this.loading = false
      }
    },

    async batchDeleteMappingMode() {
      if (this.selectedMappings.length === 0) {
        this.$message.error('请选择要删除的域名映射')
        return
      }

      // 最后确认
      const confirmed = await this.$confirm(
        `确定要删除 ${this.selectedMappings.length} 个域名吗？此操作不可逆！`,
        '最终确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error',
          confirmButtonClass: 'el-button--danger'
        }
      ).catch(() => false)

      if (!confirmed) {
        return
      }

      this.loading = true
      this.results = []

      try {
        for (const mapping of this.selectedMappings) {
          if (mapping.status !== '有效') {
            this.results.push({
              domain: mapping.domain,
              zoneId: '',
              status: '失败',
              message: '映射无效，跳过删除'
            })
            continue
          }

          try {
            const account = this.accounts.find(acc => acc.name === mapping.email)
            if (!account) {
              this.results.push({
                domain: mapping.domain,
                zoneId: '',
                status: '失败',
                message: '未找到对应的账户'
              })
              continue
            }

            // 首先获取该账户下的域名列表以找到Zone ID
            const zonesResp = await axios.get('/api/zones', {
              params: {
                email: account.name,
                key: account.token
              }
            })

            if (!zonesResp.data.result) {
              this.results.push({
                domain: mapping.domain,
                zoneId: '',
                status: '失败',
                message: '无法获取账户域名列表'
              })
              continue
            }

            const zone = zonesResp.data.result.find(z => z.name === mapping.domain)
            if (!zone) {
              this.results.push({
                domain: mapping.domain,
                zoneId: '',
                status: '失败',
                message: '在该账户下未找到此域名'
              })
              continue
            }

            // 删除Zone
            const deleteResp = await axios.delete(`/api/zones/${zone.id}`, {
              data: {
                email: account.name,
                key: account.token
              }
            })

            if (deleteResp.data.success) {
              this.results.push({
                domain: mapping.domain,
                zoneId: zone.id,
                status: '成功',
                message: 'Zone删除成功'
              })
            } else {
              this.results.push({
                domain: mapping.domain,
                zoneId: zone.id,
                status: '失败',
                message: deleteResp.data.errors?.[0]?.message || '删除失败'
              })
            }
          } catch (e) {
            this.results.push({
              domain: mapping.domain,
              zoneId: '',
              status: '失败',
              message: e.response?.data?.error || e.message
            })
          }
        }

        const successCount = this.results.filter(r => r.status === '成功').length
        const failCount = this.results.filter(r => r.status === '失败').length

        this.$message.success(`删除完成！成功: ${successCount} 个，失败: ${failCount} 个`)

        // 清空选择
        if (successCount > 0) {
          this.selectedMappings = []
          this.confirmText = ''
        }

      } catch (e) {
        console.error('批量删除错误:', e)
        this.$message.error('批量删除失败：' + e.message)
      } finally {
        this.loading = false
      }
    },

    exportResults() {
      if (this.results.length === 0) {
        this.$message.warning('没有结果可导出')
        return
      }

      const csvContent = [
        ['域名', 'Zone ID', '状态', '详细信息'].join(','),
        ...this.results.map(r => [
          r.domain,
          r.zoneId,
          r.status,
          `"${r.message}"`
        ].join(','))
      ].join('\n')

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `batch_delete_results_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success('结果已导出')
    },

    clearResults() {
      this.results = []
      this.$message.success('结果已清空')
    }
  }
}
</script>

<style scoped>
.batch-delete-zone {
  padding: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.el-checkbox {
  margin-bottom: 10px;
}

.el-alert {
  margin-bottom: 15px;
}

.el-table {
  margin-top: 15px;
}
</style>